# 支付宝账务数据插入说明

## 概述
根据Excel文件 `2088260786994890-20250801-168426223-账务组合查询-simple.xls` 生成的MySQL INSERT语句，用于向表 `dig_asset_pay_order` 插入3177条支付宝账务记录。

## 文件说明

### 1. 主要SQL文件
- **`insert_statements.sql`** - 完整的3177条INSERT语句（44,482行）
- **`dig_asset_pay_order_insert.sql`** - 优化版本，包含前10条示例数据和统计查询
- **`execute_all_batches.sql`** - 批量执行所有分批文件的主脚本

### 2. 分批文件（推荐使用）
- **`dig_asset_pay_order_batch_01.sql`** - 第1批：记录1-1000
- **`dig_asset_pay_order_batch_02.sql`** - 第2批：记录1001-2000  
- **`dig_asset_pay_order_batch_03.sql`** - 第3批：记录2001-3000
- **`dig_asset_pay_order_batch_04.sql`** - 第4批：记录3001-3177

### 3. 辅助文件
- **`parse_excel.py`** - Excel数据解析脚本
- **`create_batch_insert.py`** - 分批文件生成脚本

## 数据统计
- **总记录数**: 3,177条
- **时间范围**: 2025-07-31 10:22:00 至 2025-07-31 21:35:54
- **账务类型**: 收费、在线支付
- **支付渠道**: 支付宝账号、快捷支付-信用卡、快捷支付-借记卡、余额宝、余额支付等

## 表结构要求
确保目标表 `dig_asset_pay_order` 包含以下字段：

```sql
CREATE TABLE dig_asset_pay_order (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_number INT NOT NULL,
    entry_time DATETIME NOT NULL,
    alipay_trade_no VARCHAR(50) NOT NULL,
    alipay_serial_no VARCHAR(50) NOT NULL,
    merchant_order_no VARCHAR(50) NOT NULL,
    account_type VARCHAR(20) NOT NULL,
    income_amount DECIMAL(10,2) DEFAULT 0.00,
    expense_amount DECIMAL(10,2) DEFAULT 0.00,
    account_balance DECIMAL(10,2) NOT NULL,
    service_fee DECIMAL(10,2) DEFAULT 0.00,
    payment_channel VARCHAR(50),
    contract_product VARCHAR(50),
    counterpart_account VARCHAR(100),
    counterpart_name VARCHAR(100),
    bank_order_no VARCHAR(50),
    product_name VARCHAR(100),
    remark TEXT,
    business_base_order_no VARCHAR(50),
    business_order_no VARCHAR(100),
    business_bill_source VARCHAR(50),
    business_description VARCHAR(200),
    payment_remark TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 执行方法

### 方法1：分批执行（推荐）
```bash
# 执行所有批次
mysql -u username -p database_name < execute_all_batches.sql

# 或者单独执行每个批次
mysql -u username -p database_name < dig_asset_pay_order_batch_01.sql
mysql -u username -p database_name < dig_asset_pay_order_batch_02.sql
mysql -u username -p database_name < dig_asset_pay_order_batch_03.sql
mysql -u username -p database_name < dig_asset_pay_order_batch_04.sql
```

### 方法2：完整执行
```bash
# 执行完整的INSERT语句文件（可能较慢）
mysql -u username -p database_name < insert_statements.sql
```

### 方法3：MySQL客户端内执行
```sql
-- 在MySQL客户端中
USE your_database_name;
SOURCE /path/to/execute_all_batches.sql;
```

## 性能优化建议

### 执行前设置
```sql
SET autocommit = 0;
SET unique_checks = 0;
SET foreign_key_checks = 0;
SET sql_log_bin = 0;  -- 如果不需要二进制日志
```

### 执行后恢复
```sql
SET unique_checks = 1;
SET foreign_key_checks = 1;
SET autocommit = 1;
SET sql_log_bin = 1;
```

## 验证查询

### 基本统计
```sql
SELECT 
    COUNT(*) AS '总记录数',
    MIN(entry_time) AS '最早交易时间',
    MAX(entry_time) AS '最晚交易时间'
FROM dig_asset_pay_order;
```

### 按账务类型统计
```sql
SELECT 
    account_type AS '账务类型',
    COUNT(*) AS '交易笔数',
    SUM(income_amount) AS '收入金额',
    SUM(expense_amount) AS '支出金额'
FROM dig_asset_pay_order 
GROUP BY account_type;
```

### 按支付渠道统计
```sql
SELECT 
    payment_channel AS '支付渠道',
    COUNT(*) AS '交易笔数',
    SUM(income_amount) AS '收入金额'
FROM dig_asset_pay_order 
WHERE account_type = '在线支付'
GROUP BY payment_channel
ORDER BY COUNT(*) DESC;
```

## 注意事项

1. **备份**: 执行前请备份数据库
2. **权限**: 确保MySQL用户有INSERT权限
3. **空间**: 确保数据库有足够存储空间
4. **编码**: 文件使用UTF-8编码，确保数据库支持中文
5. **事务**: 分批文件使用事务，如果出错会回滚当前批次
6. **重复执行**: 如需重复执行，请先清空表或删除已插入的数据

## 故障排除

### 常见错误
1. **字符编码错误**: 确保数据库字符集为utf8mb4
2. **字段长度不够**: 检查VARCHAR字段长度设置
3. **数据类型不匹配**: 确保DECIMAL字段精度设置正确
4. **主键冲突**: 如果表中已有数据，可能需要调整serial_number

### 解决方案
```sql
-- 清空表重新插入
TRUNCATE TABLE dig_asset_pay_order;

-- 或者删除指定范围的数据
DELETE FROM dig_asset_pay_order WHERE serial_number BETWEEN 1 AND 3177;
```

## 联系信息
如有问题，请检查：
1. 表结构是否正确
2. 数据库连接是否正常
3. 文件路径是否正确
4. MySQL版本兼容性
