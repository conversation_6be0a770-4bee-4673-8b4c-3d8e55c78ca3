#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比temp1.xlsx和temp2.xlsx文件中的ORDER_NO字段差异
"""

import pandas as pd
import os

def compare_order_no():
    """对比两个Excel文件中的ORDER_NO字段"""
    
    # 检查文件是否存在
    if not os.path.exists('temp1.xlsx'):
        print("错误：找不到temp1.xlsx文件")
        return
    
    if not os.path.exists('temp2.xlsx'):
        print("错误：找不到temp2.xlsx文件")
        return
    
    try:
        # 读取Excel文件
        print("正在读取temp1.xlsx...")
        df1 = pd.read_excel('temp1.xlsx')
        
        print("正在读取temp2.xlsx...")
        df2 = pd.read_excel('temp2.xlsx')
        
        # 显示文件基本信息
        print(f"\ntemp1.xlsx 基本信息:")
        print(f"  行数: {len(df1)}")
        print(f"  列数: {len(df1.columns)}")
        print(f"  列名: {list(df1.columns)}")
        
        print(f"\ntemp2.xlsx 基本信息:")
        print(f"  行数: {len(df2)}")
        print(f"  列数: {len(df2.columns)}")
        print(f"  列名: {list(df2.columns)}")
        
        # 检查ORDER_NO列是否存在
        order_no_col1 = None
        order_no_col2 = None
        
        # 在temp1.xlsx中查找ORDER_NO列
        for col in df1.columns:
            if 'ORDER_NO' in str(col).upper():
                order_no_col1 = col
                break
        
        # 在temp2.xlsx中查找order_no列
        for col in df2.columns:
            if 'ORDER_NO' in str(col).upper():
                order_no_col2 = col
                break
        
        if order_no_col1 is None:
            print(f"\n错误：在temp1.xlsx中找不到ORDER_NO列")
            return
        
        if order_no_col2 is None:
            print(f"\n错误：在temp2.xlsx中找不到order_no列")
            return
        
        print(f"\n找到列:")
        print(f"  temp1.xlsx中的ORDER_NO列: {order_no_col1}")
        print(f"  temp2.xlsx中的order_no列: {order_no_col2}")
        
        # 提取ORDER_NO数据并去重
        order_no_set1 = set(df1[order_no_col1].dropna().astype(str))
        order_no_set2 = set(df2[order_no_col2].dropna().astype(str))
        
        print(f"\n数据统计:")
        print(f"  temp1.xlsx中唯一ORDER_NO数量: {len(order_no_set1)}")
        print(f"  temp2.xlsx中唯一order_no数量: {len(order_no_set2)}")
        
        # 计算差异
        only_in_temp1 = order_no_set1 - order_no_set2
        only_in_temp2 = order_no_set2 - order_no_set1
        common = order_no_set1 & order_no_set2
        
        print(f"\n对比结果:")
        print(f"  共同的ORDER_NO数量: {len(common)}")
        print(f"  仅在temp1.xlsx中的ORDER_NO数量: {len(only_in_temp1)}")
        print(f"  仅在temp2.xlsx中的order_no数量: {len(only_in_temp2)}")
        
        # 保存差异结果到文件
        if only_in_temp1:
            print(f"\n仅在temp1.xlsx中的ORDER_NO (前10个):")
            for i, order_no in enumerate(sorted(only_in_temp1)):
                if i < 10:
                    print(f"  {order_no}")
                else:
                    print(f"  ... 还有 {len(only_in_temp1) - 10} 个")
                    break
        
        if only_in_temp2:
            print(f"\n仅在temp2.xlsx中的order_no (前10个):")
            for i, order_no in enumerate(sorted(only_in_temp2)):
                if i < 10:
                    print(f"  {order_no}")
                else:
                    print(f"  ... 还有 {len(only_in_temp2) - 10} 个")
                    break
        
        # 将差异结果保存到Excel文件
        result_data = {
            '仅在temp1中的ORDER_NO': list(only_in_temp1) + [''] * max(0, len(only_in_temp2) - len(only_in_temp1)),
            '仅在temp2中的order_no': list(only_in_temp2) + [''] * max(0, len(only_in_temp1) - len(only_in_temp2))
        }
        
        # 确保两列长度相同
        max_len = max(len(only_in_temp1), len(only_in_temp2))
        result_data['仅在temp1中的ORDER_NO'] = list(only_in_temp1) + [''] * (max_len - len(only_in_temp1))
        result_data['仅在temp2中的order_no'] = list(only_in_temp2) + [''] * (max_len - len(only_in_temp2))
        
        result_df = pd.DataFrame(result_data)
        result_filename = 'order_no_comparison_result.xlsx'
        result_df.to_excel(result_filename, index=False)
        print(f"\n差异结果已保存到: {result_filename}")
        
        # 保存详细统计信息
        summary_filename = 'order_no_comparison_summary.txt'
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write("ORDER_NO字段对比结果摘要\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"temp1.xlsx:\n")
            f.write(f"  总行数: {len(df1)}\n")
            f.write(f"  ORDER_NO列名: {order_no_col1}\n")
            f.write(f"  唯一ORDER_NO数量: {len(order_no_set1)}\n\n")
            f.write(f"temp2.xlsx:\n")
            f.write(f"  总行数: {len(df2)}\n")
            f.write(f"  order_no列名: {order_no_col2}\n")
            f.write(f"  唯一order_no数量: {len(order_no_set2)}\n\n")
            f.write(f"对比结果:\n")
            f.write(f"  共同的ORDER_NO数量: {len(common)}\n")
            f.write(f"  仅在temp1.xlsx中的ORDER_NO数量: {len(only_in_temp1)}\n")
            f.write(f"  仅在temp2.xlsx中的order_no数量: {len(only_in_temp2)}\n")
        
        print(f"详细摘要已保存到: {summary_filename}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    compare_order_no()
