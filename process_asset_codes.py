import pandas as pd
import re

def split_asset_codes(asset_code_str):
    """
    拆分ASSET_CODE字符串，处理逗号分隔的情况
    """
    if pd.isna(asset_code_str):
        return []
    
    # 转换为字符串并去除首尾空格
    asset_code_str = str(asset_code_str).strip()
    
    if not asset_code_str:
        return []
    
    # 按逗号分割并去除每个部分的空格
    codes = [code.strip() for code in asset_code_str.split(',')]
    
    # 过滤掉空字符串
    codes = [code for code in codes if code]
    
    return codes

def main():
    print("开始处理Excel文件...")
    
    # 读取dig_asset_order_2.xlsx
    print("读取dig_asset_order_2.xlsx...")
    try:
        df_order = pd.read_excel('dig_asset_order_2.xlsx')
        print(f"dig_asset_order_2.xlsx 列名: {list(df_order.columns)}")
        print(f"dig_asset_order_2.xlsx 数据行数: {len(df_order)}")
    except Exception as e:
        print(f"读取dig_asset_order_2.xlsx失败: {e}")
        return
    
    # 读取dig_data_asset_2.xlsx
    print("\n读取dig_data_asset_2.xlsx...")
    try:
        df_asset = pd.read_excel('dig_data_asset_2.xlsx')
        print(f"dig_data_asset_2.xlsx 列名: {list(df_asset.columns)}")
        print(f"dig_data_asset_2.xlsx 数据行数: {len(df_asset)}")
    except Exception as e:
        print(f"读取dig_data_asset_2.xlsx失败: {e}")
        return
    
    # 检查ASSET_CODE列是否存在
    if 'ASSET_CODE' not in df_order.columns:
        print("错误: dig_asset_order_2.xlsx中没有找到ASSET_CODE列")
        return
    
    if 'ASSET_CODE' not in df_asset.columns:
        print("错误: dig_data_asset_2.xlsx中没有找到ASSET_CODE列")
        return
    
    # 从dig_data_asset_2.xlsx获取所有ASSET_CODE
    existing_asset_codes = set()
    for asset_code in df_asset['ASSET_CODE']:
        if not pd.isna(asset_code):
            codes = split_asset_codes(asset_code)
            existing_asset_codes.update(codes)
    
    print(f"\ndig_data_asset_2.xlsx中的ASSET_CODE数量: {len(existing_asset_codes)}")
    print(f"前10个ASSET_CODE: {list(existing_asset_codes)[:10]}")
    
    # 从dig_asset_order_2.xlsx获取所有ASSET_CODE并拆分
    order_asset_codes = set()
    order_asset_code_details = []  # 保存详细信息用于后续分析
    
    for index, row in df_order.iterrows():
        asset_code = row['ASSET_CODE']
        if not pd.isna(asset_code):
            codes = split_asset_codes(asset_code)
            order_asset_codes.update(codes)
            
            # 保存详细信息
            for code in codes:
                order_asset_code_details.append({
                    'row_index': index,
                    'original_asset_code': asset_code,
                    'split_asset_code': code,
                    'other_columns': {col: row[col] for col in df_order.columns if col != 'ASSET_CODE'}
                })
    
    print(f"\ndig_asset_order_2.xlsx中的ASSET_CODE数量: {len(order_asset_codes)}")
    print(f"前10个ASSET_CODE: {list(order_asset_codes)[:10]}")
    
    # 找出不在dig_data_asset_2.xlsx中的ASSET_CODE
    missing_asset_codes = order_asset_codes - existing_asset_codes
    
    print(f"\n不在dig_data_asset_2.xlsx中的ASSET_CODE数量: {len(missing_asset_codes)}")
    
    if missing_asset_codes:
        print("\n缺失的ASSET_CODE列表:")
        for i, code in enumerate(sorted(missing_asset_codes), 1):
            print(f"{i:3d}. {code}")
        
        # 找出包含缺失ASSET_CODE的原始行信息
        print("\n包含缺失ASSET_CODE的原始行信息:")
        missing_details = []
        for detail in order_asset_code_details:
            if detail['split_asset_code'] in missing_asset_codes:
                missing_details.append(detail)
        
        # 按行索引排序
        missing_details.sort(key=lambda x: x['row_index'])
        
        for detail in missing_details:
            print(f"\n行 {detail['row_index'] + 1}:")
            print(f"  原始ASSET_CODE: {detail['original_asset_code']}")
            print(f"  拆分后的ASSET_CODE: {detail['split_asset_code']}")
            for col, value in detail['other_columns'].items():
                if not pd.isna(value):
                    print(f"  {col}: {value}")
        
        # 保存结果到文件
        print(f"\n保存缺失的ASSET_CODE到文件...")
        
        # 保存缺失的ASSET_CODE列表
        with open('missing_asset_codes.txt', 'w', encoding='utf-8') as f:
            f.write("不在dig_data_asset_2.xlsx中的ASSET_CODE:\n")
            f.write("=" * 50 + "\n")
            for i, code in enumerate(sorted(missing_asset_codes), 1):
                f.write(f"{i:3d}. {code}\n")
        
        # 保存详细信息到CSV
        missing_df = pd.DataFrame([
            {
                'row_index': detail['row_index'] + 1,
                'original_asset_code': detail['original_asset_code'],
                'missing_asset_code': detail['split_asset_code'],
                **detail['other_columns']
            }
            for detail in missing_details
        ])
        
        missing_df.to_csv('missing_asset_codes_details.csv', index=False, encoding='utf-8-sig')
        
        print("结果已保存到:")
        print("- missing_asset_codes.txt: 缺失的ASSET_CODE列表")
        print("- missing_asset_codes_details.csv: 包含缺失ASSET_CODE的详细行信息")
    
    else:
        print("所有ASSET_CODE都存在于dig_data_asset_2.xlsx中")
    
    print("\n处理完成!")

if __name__ == "__main__":
    main()
