import pandas as pd
from datetime import datetime

def compare_904_with_asset_file():
    """
    用904_asset_codes文件中的split_asset_code与dig_data_asset_2.xlsx对比
    找出缺失的ASSET_CODE并导出到新Excel
    """
    print("开始对比904个ASSET_CODE与dig_data_asset_2.xlsx...")
    print("=" * 60)
    
    # 1. 读取904个ASSET_CODE文件
    print("1. 读取904_asset_codes_20250731_210732.xlsx...")
    df_904 = pd.read_excel('904_asset_codes_20250731_210732.xlsx', sheet_name='全部904个ASSET_CODE条目')
    print(f"   读取到904个条目: {len(df_904)}")
    print(f"   列名: {list(df_904.columns)}")
    
    # 2. 读取dig_data_asset_2.xlsx
    print("\n2. 读取dig_data_asset_2.xlsx...")
    df_asset = pd.read_excel('dig_data_asset_2.xlsx')
    print(f"   读取到资产数据: {len(df_asset)} 行")
    print(f"   列名: {list(df_asset.columns)}")
    
    # 3. 提取dig_data_asset_2.xlsx中的所有ASSET_CODE
    print("\n3. 提取dig_data_asset_2.xlsx中的ASSET_CODE...")
    existing_asset_codes = set()
    
    for asset_code in df_asset['ASSET_CODE']:
        if not pd.isna(asset_code):
            asset_code_str = str(asset_code).strip()
            if asset_code_str:
                # 处理可能的逗号分隔
                codes = [code.strip() for code in asset_code_str.split(',')]
                codes = [code for code in codes if code]
                existing_asset_codes.update(codes)
    
    print(f"   dig_data_asset_2.xlsx中的唯一ASSET_CODE数量: {len(existing_asset_codes)}")
    
    # 4. 逐一对比904个split_asset_code
    print("\n4. 逐一对比904个split_asset_code...")
    missing_entries = []
    existing_entries = []
    
    for index, row in df_904.iterrows():
        split_asset_code = row['split_asset_code']
        
        # 检查是否存在于dig_data_asset_2.xlsx中
        exists = split_asset_code in existing_asset_codes
        
        entry_info = {
            'entry_id': row['entry_id'],
            'original_row_index': row['original_row_index'],
            'ORDER_ID': row['ORDER_ID'],
            'BUYER_ID': row['BUYER_ID'],
            'BUY_QUANTITY': row['BUY_QUANTITY'],
            'PAY_TIME': row['PAY_TIME'],
            'original_asset_code': row['original_asset_code'],
            'split_sequence': row['split_sequence'],
            'split_asset_code': split_asset_code,
            'total_splits_in_row': row['total_splits_in_row'],
            'exists_in_asset_file': exists
        }
        
        if exists:
            existing_entries.append(entry_info)
        else:
            missing_entries.append(entry_info)
    
    print(f"   存在的条目数: {len(existing_entries)}")
    print(f"   缺失的条目数: {len(missing_entries)}")
    
    # 5. 创建对比结果Excel文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"missing_asset_codes_comparison_{timestamp}.xlsx"
    
    print(f"\n5. 创建Excel文件: {excel_filename}")
    
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        
        # 工作表1: 缺失的ASSET_CODE条目
        if missing_entries:
            df_missing = pd.DataFrame(missing_entries)
            df_missing = df_missing.sort_values(['entry_id'])
            df_missing.to_excel(writer, sheet_name='缺失的ASSET_CODE条目', index=False)
            print(f"   工作表 '缺失的ASSET_CODE条目' 创建完成: {len(df_missing)} 条记录")
        else:
            # 如果没有缺失的，创建一个空的工作表
            df_empty = pd.DataFrame({'说明': ['没有发现缺失的ASSET_CODE']})
            df_empty.to_excel(writer, sheet_name='缺失的ASSET_CODE条目', index=False)
            print(f"   工作表 '缺失的ASSET_CODE条目' 创建完成: 无缺失记录")
        
        # 工作表2: 缺失ASSET_CODE的唯一列表
        if missing_entries:
            unique_missing_codes = sorted(set(entry['split_asset_code'] for entry in missing_entries))
            
            unique_missing_data = []
            for code in unique_missing_codes:
                # 找到包含这个ASSET_CODE的所有缺失条目
                related_entries = [entry for entry in missing_entries if entry['split_asset_code'] == code]
                
                unique_missing_data.append({
                    'MISSING_ASSET_CODE': code,
                    'OCCURRENCE_COUNT': len(related_entries),
                    'ORDER_IDS': ', '.join(str(entry['ORDER_ID']) for entry in related_entries),
                    'BUYER_IDS': ', '.join(str(entry['BUYER_ID']) for entry in related_entries),
                    'TOTAL_QUANTITY': sum(entry['BUY_QUANTITY'] for entry in related_entries),
                    'PAY_TIMES': ', '.join(str(entry['PAY_TIME']) for entry in related_entries)
                })
            
            df_unique_missing = pd.DataFrame(unique_missing_data)
            df_unique_missing.to_excel(writer, sheet_name='缺失ASSET_CODE唯一列表', index=False)
            print(f"   工作表 '缺失ASSET_CODE唯一列表' 创建完成: {len(df_unique_missing)} 个唯一ASSET_CODE")
        else:
            df_empty = pd.DataFrame({'说明': ['没有发现缺失的ASSET_CODE']})
            df_empty.to_excel(writer, sheet_name='缺失ASSET_CODE唯一列表', index=False)
            print(f"   工作表 '缺失ASSET_CODE唯一列表' 创建完成: 无缺失记录")
        
        # 工作表3: 完整对比结果
        all_comparison_entries = existing_entries + missing_entries
        df_all_comparison = pd.DataFrame(all_comparison_entries)
        df_all_comparison = df_all_comparison.sort_values(['entry_id'])
        df_all_comparison.to_excel(writer, sheet_name='完整对比结果', index=False)
        print(f"   工作表 '完整对比结果' 创建完成: {len(df_all_comparison)} 条记录")
        
        # 工作表4: 统计汇总
        unique_missing_count = len(set(entry['split_asset_code'] for entry in missing_entries)) if missing_entries else 0
        
        stats_data = [
            ['对比统计', ''],
            ['904个条目总数', len(df_904)],
            ['dig_data_asset_2.xlsx ASSET_CODE数量', len(existing_asset_codes)],
            ['', ''],
            ['对比结果', ''],
            ['存在的条目数', len(existing_entries)],
            ['缺失的条目数', len(missing_entries)],
            ['缺失的唯一ASSET_CODE数量', unique_missing_count],
            ['', ''],
            ['缺失率统计', ''],
            ['条目缺失率', f"{len(missing_entries) / len(df_904) * 100:.2f}%" if len(df_904) > 0 else "0%"],
            ['唯一ASSET_CODE缺失率', f"{unique_missing_count / df_904['split_asset_code'].nunique() * 100:.2f}%" if df_904['split_asset_code'].nunique() > 0 else "0%"]
        ]
        
        df_stats = pd.DataFrame(stats_data, columns=['统计项目', '数值'])
        df_stats.to_excel(writer, sheet_name='统计汇总', index=False)
        print(f"   工作表 '统计汇总' 创建完成")
    
    print(f"\n✅ Excel文件创建成功: {excel_filename}")
    
    # 显示统计结果
    print(f"\n📊 对比结果统计:")
    print(f"总条目数: {len(df_904)}")
    print(f"存在的条目数: {len(existing_entries)}")
    print(f"缺失的条目数: {len(missing_entries)}")
    print(f"缺失的唯一ASSET_CODE数量: {len(set(entry['split_asset_code'] for entry in missing_entries)) if missing_entries else 0}")
    print(f"条目缺失率: {len(missing_entries) / len(df_904) * 100:.2f}%" if len(df_904) > 0 else "0%")
    
    # 显示前10个缺失的ASSET_CODE
    if missing_entries:
        unique_missing = sorted(set(entry['split_asset_code'] for entry in missing_entries))
        print(f"\n📋 前10个缺失的ASSET_CODE:")
        for i, code in enumerate(unique_missing[:10], 1):
            count = len([entry for entry in missing_entries if entry['split_asset_code'] == code])
            print(f"   {i:2d}. {code} (在904个条目中出现{count}次)")
        
        if len(unique_missing) > 10:
            print(f"   ... 还有 {len(unique_missing) - 10} 个")
    else:
        print(f"\n🎉 所有904个ASSET_CODE都存在于dig_data_asset_2.xlsx中！")
    
    return excel_filename, len(missing_entries), len(set(entry['split_asset_code'] for entry in missing_entries)) if missing_entries else 0

if __name__ == "__main__":
    excel_file, missing_count, unique_missing_count = compare_904_with_asset_file()
    print(f"\n🎯 对比完成:")
    print(f"结果文件: {excel_file}")
    print(f"缺失条目数: {missing_count}")
    print(f"缺失唯一ASSET_CODE数量: {unique_missing_count}")
