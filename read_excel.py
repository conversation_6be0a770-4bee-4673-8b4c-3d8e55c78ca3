import pandas as pd

# 读取Excel文件
df = pd.read_excel('dig_asset_order.xlsx', sheet_name='update_dig_asset_order')

print('列名:')
print(list(df.columns))
print(f'\n数据行数: {len(df)}')

print('\n前5行数据:')
for i in range(min(5, len(df))):
    print(f'第{i+1}行:')
    for col in df.columns:
        value = df.iloc[i][col]
        if pd.isna(value):
            value = 'NULL'
        print(f'  {col}: {value}')
    print()

# 显示所有唯一的ORDER_ID
print('所有ORDER_ID:')
order_ids = df['ORDER_ID'].unique()
print(order_ids)
