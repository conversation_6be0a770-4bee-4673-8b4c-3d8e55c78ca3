import pandas as pd

# 读取Excel文件中的update_dig_data_asset工作表
try:
    df = pd.read_excel('dig_asset_order.xlsx', sheet_name='update_dig_data_asset')
    
    print('列名:')
    print(list(df.columns))
    print(f'\n数据行数: {len(df)}')
    
    print('\n前5行数据:')
    for i in range(min(5, len(df))):
        print(f'第{i+1}行:')
        for col in df.columns:
            value = df.iloc[i][col]
            if pd.isna(value):
                value = 'NULL'
            print(f'  {col}: {value}')
        print()
        
except Exception as e:
    print(f'读取Excel文件时出错: {e}')
    
    # 尝试列出所有工作表名称
    try:
        xl_file = pd.ExcelFile('dig_asset_order.xlsx')
        print('\n可用的工作表名称:')
        for sheet_name in xl_file.sheet_names:
            print(f'  - {sheet_name}')
    except Exception as e2:
        print(f'无法读取工作表列表: {e2}')
