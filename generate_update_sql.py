import pandas as pd
from datetime import datetime

def format_value(value, column_name):
    """格式化值用于SQL语句"""
    if pd.isna(value) or value is None:
        return 'NULL'
    
    # 字符串类型字段需要加引号
    string_fields = ['ORDER_NO', 'TRADE_TYPE', 'PAY_CHANNEL', 'ASSET_NAME', 
                    'ASSET_CODE', 'REFUND_ORDER_NO', 'STATUS_CD', 'sharing_rule', 'UNION_ORDER_NO']
    
    if column_name in string_fields:
        # 转义单引号
        escaped_value = str(value).replace("'", "''")
        return f"'{escaped_value}'"
    
    # 日期时间字段
    datetime_fields = ['PAY_TIME', 'CLOSE_TIME', 'CREATE_DATE', 'UPDATE_DATE']
    if column_name in datetime_fields:
        if isinstance(value, str):
            return f"'{value}'"
        elif hasattr(value, 'strftime'):
            return f"'{value.strftime('%Y-%m-%d %H:%M:%S')}'"
        else:
            return f"'{str(value)}'"
    
    # 数值类型直接返回
    return str(value)

def generate_update_statements():
    """生成UPDATE语句"""
    # 读取Excel文件
    df = pd.read_excel('dig_asset_order.xlsx', sheet_name='update_dig_asset_order')
    
    update_statements = []
    
    for index, row in df.iterrows():
        order_id = row['ORDER_ID']
        
        # 构建SET子句
        set_clauses = []
        for column in df.columns:
            if column != 'ORDER_ID':  # 排除WHERE条件中的字段
                value = format_value(row[column], column)
                set_clauses.append(f"`{column}` = {value}")
        
        # 生成UPDATE语句
        update_sql = f"""UPDATE `dig_asset_order` 
SET {', '.join(set_clauses)}
WHERE `ORDER_ID` = {order_id};"""
        
        update_statements.append(update_sql)
    
    return update_statements

# 生成UPDATE语句
statements = generate_update_statements()

# 输出到文件
with open('update_dig_asset_order.sql', 'w', encoding='utf-8') as f:
    f.write("-- 数字资产订单表更新语句\n")
    f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"-- 总计: {len(statements)} 条更新语句\n\n")
    
    for i, statement in enumerate(statements, 1):
        f.write(f"-- 第 {i} 条更新语句\n")
        f.write(statement)
        f.write("\n\n")

print(f"已生成 {len(statements)} 条UPDATE语句，保存到 update_dig_asset_order.sql 文件中")
print("\n前3条语句预览:")
for i, statement in enumerate(statements[:3], 1):
    print(f"\n第 {i} 条:")
    print(statement)
