#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比temp3.xlsx文件中order_no_1和order_no_2列之间的差异
"""

import pandas as pd
import os

def compare_temp3_columns():
    """对比temp3.xlsx中order_no_1和order_no_2列的差异"""
    
    # 检查文件是否存在
    if not os.path.exists('temp3.xlsx'):
        print("错误：找不到temp3.xlsx文件")
        return
    
    try:
        # 读取Excel文件
        print("正在读取temp3.xlsx...")
        df = pd.read_excel('temp3.xlsx')
        
        # 显示文件基本信息
        print(f"\ntemp3.xlsx 基本信息:")
        print(f"  行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        # 检查order_no_1和order_no_2列是否存在
        order_no_1_col = None
        order_no_2_col = None
        
        # 查找order_no_1列
        for col in df.columns:
            if 'order_no_1' in str(col).lower():
                order_no_1_col = col
                break
        
        # 查找order_no_2列
        for col in df.columns:
            if 'order_no_2' in str(col).lower():
                order_no_2_col = col
                break
        
        if order_no_1_col is None:
            print(f"\n错误：在temp3.xlsx中找不到order_no_1列")
            print(f"可用的列名: {list(df.columns)}")
            return
        
        if order_no_2_col is None:
            print(f"\n错误：在temp3.xlsx中找不到order_no_2列")
            print(f"可用的列名: {list(df.columns)}")
            return
        
        print(f"\n找到列:")
        print(f"  order_no_1列: {order_no_1_col}")
        print(f"  order_no_2列: {order_no_2_col}")
        
        # 提取数据并去重，处理空值
        order_no_1_data = df[order_no_1_col].dropna().astype(str)
        order_no_2_data = df[order_no_2_col].dropna().astype(str)
        
        # 去除字符串中的'nan'值（如果有的话）
        order_no_1_data = order_no_1_data[order_no_1_data != 'nan']
        order_no_2_data = order_no_2_data[order_no_2_data != 'nan']
        
        order_no_1_set = set(order_no_1_data)
        order_no_2_set = set(order_no_2_data)
        
        print(f"\n数据统计:")
        print(f"  order_no_1列非空值数量: {len(order_no_1_data)}")
        print(f"  order_no_1列唯一值数量: {len(order_no_1_set)}")
        print(f"  order_no_2列非空值数量: {len(order_no_2_data)}")
        print(f"  order_no_2列唯一值数量: {len(order_no_2_set)}")
        
        # 计算差异
        only_in_order_no_1 = order_no_1_set - order_no_2_set
        only_in_order_no_2 = order_no_2_set - order_no_1_set
        common = order_no_1_set & order_no_2_set
        
        print(f"\n对比结果:")
        print(f"  共同的订单号数量: {len(common)}")
        print(f"  仅在order_no_1中的订单号数量: {len(only_in_order_no_1)}")
        print(f"  仅在order_no_2中的订单号数量: {len(only_in_order_no_2)}")
        
        # 显示部分差异数据
        if only_in_order_no_1:
            print(f"\n仅在order_no_1中的订单号 (前10个):")
            for i, order_no in enumerate(sorted(only_in_order_no_1)):
                if i < 10:
                    print(f"  {order_no}")
                else:
                    print(f"  ... 还有 {len(only_in_order_no_1) - 10} 个")
                    break
        
        if only_in_order_no_2:
            print(f"\n仅在order_no_2中的订单号 (前10个):")
            for i, order_no in enumerate(sorted(only_in_order_no_2)):
                if i < 10:
                    print(f"  {order_no}")
                else:
                    print(f"  ... 还有 {len(only_in_order_no_2) - 10} 个")
                    break
        
        # 将差异结果保存到Excel文件
        max_len = max(len(only_in_order_no_1), len(only_in_order_no_2))
        
        result_data = {
            '仅在order_no_1中的订单号': list(only_in_order_no_1) + [''] * (max_len - len(only_in_order_no_1)),
            '仅在order_no_2中的订单号': list(only_in_order_no_2) + [''] * (max_len - len(only_in_order_no_2))
        }
        
        result_df = pd.DataFrame(result_data)
        result_filename = 'temp3_columns_comparison_result.xlsx'
        result_df.to_excel(result_filename, index=False)
        print(f"\n差异结果已保存到: {result_filename}")
        
        # 保存详细统计信息
        summary_filename = 'temp3_columns_comparison_summary.txt'
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write("temp3.xlsx中order_no_1和order_no_2列对比结果摘要\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"文件信息:\n")
            f.write(f"  总行数: {len(df)}\n")
            f.write(f"  总列数: {len(df.columns)}\n")
            f.write(f"  order_no_1列名: {order_no_1_col}\n")
            f.write(f"  order_no_2列名: {order_no_2_col}\n\n")
            f.write(f"数据统计:\n")
            f.write(f"  order_no_1列非空值数量: {len(order_no_1_data)}\n")
            f.write(f"  order_no_1列唯一值数量: {len(order_no_1_set)}\n")
            f.write(f"  order_no_2列非空值数量: {len(order_no_2_data)}\n")
            f.write(f"  order_no_2列唯一值数量: {len(order_no_2_set)}\n\n")
            f.write(f"对比结果:\n")
            f.write(f"  共同的订单号数量: {len(common)}\n")
            f.write(f"  仅在order_no_1中的订单号数量: {len(only_in_order_no_1)}\n")
            f.write(f"  仅在order_no_2中的订单号数量: {len(only_in_order_no_2)}\n")
        
        print(f"详细摘要已保存到: {summary_filename}")
        
        # 如果有重复值，也进行分析
        order_no_1_duplicates = order_no_1_data[order_no_1_data.duplicated()].unique()
        order_no_2_duplicates = order_no_2_data[order_no_2_data.duplicated()].unique()
        
        if len(order_no_1_duplicates) > 0 or len(order_no_2_duplicates) > 0:
            print(f"\n重复值分析:")
            if len(order_no_1_duplicates) > 0:
                print(f"  order_no_1列中有重复值: {len(order_no_1_duplicates)}个")
                print(f"  重复值示例: {list(order_no_1_duplicates[:5])}")
            if len(order_no_2_duplicates) > 0:
                print(f"  order_no_2列中有重复值: {len(order_no_2_duplicates)}个")
                print(f"  重复值示例: {list(order_no_2_duplicates[:5])}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    compare_temp3_columns()
