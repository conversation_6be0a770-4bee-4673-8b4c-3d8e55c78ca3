import pandas as pd
import re

def extract_asset_codes_from_sql():
    """
    从SQL文件中提取所有ASSET_CODE
    """
    asset_codes = []
    
    with open('update_dig_asset_order.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取ASSET_CODE
    pattern = r"`ASSET_CODE` = '([^']+)'"
    matches = re.findall(pattern, content)
    
    return set(matches)

def analyze_differences():
    """
    详细分析差异的原因
    """
    print("=== 详细差异分析 ===\n")
    
    # 1. 从SQL文件提取ASSET_CODE
    sql_asset_codes = extract_asset_codes_from_sql()
    print(f"1. SQL文件中的ASSET_CODE数量: {len(sql_asset_codes)}")
    print(f"   前10个: {list(sql_asset_codes)[:10]}")
    
    # 2. 从dig_asset_order_2.xlsx提取ASSET_CODE
    df_order = pd.read_excel('dig_asset_order_2.xlsx')
    order_asset_codes = set()
    
    for asset_code in df_order['ASSET_CODE']:
        if not pd.isna(asset_code):
            # 处理逗号分隔的情况
            codes = [code.strip() for code in str(asset_code).split(',')]
            codes = [code for code in codes if code]
            order_asset_codes.update(codes)
    
    print(f"\n2. dig_asset_order_2.xlsx中的ASSET_CODE数量: {len(order_asset_codes)}")
    print(f"   前10个: {list(order_asset_codes)[:10]}")
    
    # 3. 从dig_data_asset_2.xlsx提取ASSET_CODE
    df_asset = pd.read_excel('dig_data_asset_2.xlsx')
    existing_asset_codes = set()
    
    for asset_code in df_asset['ASSET_CODE']:
        if not pd.isna(asset_code):
            codes = [code.strip() for code in str(asset_code).split(',')]
            codes = [code for code in codes if code]
            existing_asset_codes.update(codes)
    
    print(f"\n3. dig_data_asset_2.xlsx中的ASSET_CODE数量: {len(existing_asset_codes)}")
    print(f"   前10个: {list(existing_asset_codes)[:10]}")
    
    # 4. 比较SQL文件和Excel文件的差异
    sql_vs_order = sql_asset_codes - order_asset_codes
    order_vs_sql = order_asset_codes - sql_asset_codes
    
    print(f"\n4. SQL文件与dig_asset_order_2.xlsx的差异:")
    print(f"   SQL中有但Excel中没有: {len(sql_vs_order)} 个")
    if sql_vs_order:
        print(f"   示例: {list(sql_vs_order)[:5]}")
    
    print(f"   Excel中有但SQL中没有: {len(order_vs_sql)} 个")
    if order_vs_sql:
        print(f"   示例: {list(order_vs_sql)[:5]}")
    
    # 5. 计算不同方法的缺失ASSET_CODE
    print(f"\n5. 缺失ASSET_CODE计算:")
    
    # 方法1: 基于dig_asset_order_2.xlsx
    missing_from_order = order_asset_codes - existing_asset_codes
    print(f"   基于dig_asset_order_2.xlsx: {len(missing_from_order)} 个缺失")
    
    # 方法2: 基于SQL文件
    missing_from_sql = sql_asset_codes - existing_asset_codes
    print(f"   基于SQL文件: {len(missing_from_sql)} 个缺失")
    
    # 6. 详细分析缺失的差异
    only_in_order_missing = missing_from_order - missing_from_sql
    only_in_sql_missing = missing_from_sql - missing_from_order
    common_missing = missing_from_order & missing_from_sql
    
    print(f"\n6. 缺失ASSET_CODE的详细分析:")
    print(f"   两种方法都认为缺失: {len(common_missing)} 个")
    print(f"   只有Excel方法认为缺失: {len(only_in_order_missing)} 个")
    print(f"   只有SQL方法认为缺失: {len(only_in_sql_missing)} 个")
    
    if only_in_order_missing:
        print(f"\n   只有Excel方法认为缺失的ASSET_CODE (前10个):")
        for i, code in enumerate(sorted(only_in_order_missing)[:10], 1):
            print(f"     {i:2d}. {code}")
    
    if only_in_sql_missing:
        print(f"\n   只有SQL方法认为缺失的ASSET_CODE (前10个):")
        for i, code in enumerate(sorted(only_in_sql_missing)[:10], 1):
            print(f"     {i:2d}. {code}")
    
    # 7. 检查数据范围
    print(f"\n7. ASSET_CODE数据范围分析:")
    
    def get_code_numbers(codes):
        numbers = []
        for code in codes:
            if '#' in code:
                try:
                    num = int(code.split('#')[-1])
                    numbers.append(num)
                except:
                    pass
        return numbers
    
    sql_numbers = get_code_numbers(sql_asset_codes)
    order_numbers = get_code_numbers(order_asset_codes)
    existing_numbers = get_code_numbers(existing_asset_codes)
    
    if sql_numbers:
        print(f"   SQL文件ASSET_CODE范围: {min(sql_numbers)} - {max(sql_numbers)}")
    if order_numbers:
        print(f"   Excel订单ASSET_CODE范围: {min(order_numbers)} - {max(order_numbers)}")
    if existing_numbers:
        print(f"   Excel资产ASSET_CODE范围: {min(existing_numbers)} - {max(existing_numbers)}")
    
    # 8. 保存详细结果
    print(f"\n8. 保存详细分析结果...")
    
    with open('detailed_comparison_result.txt', 'w', encoding='utf-8') as f:
        f.write("详细差异分析结果\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"SQL文件中的ASSET_CODE数量: {len(sql_asset_codes)}\n")
        f.write(f"dig_asset_order_2.xlsx中的ASSET_CODE数量: {len(order_asset_codes)}\n")
        f.write(f"dig_data_asset_2.xlsx中的ASSET_CODE数量: {len(existing_asset_codes)}\n\n")
        
        f.write(f"基于dig_asset_order_2.xlsx的缺失数量: {len(missing_from_order)}\n")
        f.write(f"基于SQL文件的缺失数量: {len(missing_from_sql)}\n\n")
        
        f.write("基于SQL文件的缺失ASSET_CODE:\n")
        f.write("-" * 30 + "\n")
        for i, code in enumerate(sorted(missing_from_sql), 1):
            f.write(f"{i:3d}. {code}\n")
    
    print("   结果已保存到 detailed_comparison_result.txt")
    
    return {
        'sql_codes': sql_asset_codes,
        'order_codes': order_asset_codes,
        'existing_codes': existing_asset_codes,
        'missing_from_sql': missing_from_sql,
        'missing_from_order': missing_from_order
    }

if __name__ == "__main__":
    results = analyze_differences()
