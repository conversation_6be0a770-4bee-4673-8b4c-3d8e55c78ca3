/*
 Navicat Premium Dump SQL

 Source Server         : 省文化数据·生产(172.20.20.2_33061)
 Source Server Type    : MySQL
 Source Server Version : 80034 (8.0.34-magnus-server)
 Source Host           : jump.tianfutv.com:33061
 Source Schema         : cwsf

 Target Server Type    : MySQL
 Target Server Version : 80034 (8.0.34-magnus-server)
 File Encoding         : 65001

 Date: 31/07/2025 17:24:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dig_data_asset
-- ----------------------------
DROP TABLE IF EXISTS `dig_data_asset`;
CREATE TABLE `dig_data_asset`  (
  `DATA_ASSET_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '数据资产ID',
  `USER_ID` bigint NOT NULL COMMENT '所属用户ID',
  `ACQUIRE_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '获取方式',
  `ACQUIRE_RECORD_ID` bigint NOT NULL COMMENT '获取记录ID',
  `ASSET_ID` bigint NOT NULL COMMENT '资产ID',
  `ASSET_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '资产名称',
  `ASSET_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '资产唯一编号',
  `CHAIN_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '链上ID',
  `CHAIN_HASH` varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '链上HASH',
  `CHAIN_TIME` datetime NULL DEFAULT NULL COMMENT '上链时间',
  `IS_REDEEMED` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已兑换权益(0未兑换 1已兑换)',
  `VERSION` int NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
  `STATUS_CD` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态',
  `STATUS_DATE` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '状态时间',
  `CREATE_DATE` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_DATE` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`DATA_ASSET_ID`) USING BTREE,
  UNIQUE INDEX `UK_ASSET_CODE`(`ASSET_CODE` ASC) USING BTREE,
  UNIQUE INDEX `UK_CHAIN_HASH`(`CHAIN_HASH` ASC) USING BTREE,
  INDEX `IDX_USER_ID`(`USER_ID` ASC) USING BTREE,
  INDEX `IDX_ASSET_ID`(`ASSET_ID` ASC) USING BTREE,
  INDEX `IDX_ACQUIRE_RECORD`(`ACQUIRE_TYPE` ASC, `ACQUIRE_RECORD_ID` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1732 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户数据资产表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
