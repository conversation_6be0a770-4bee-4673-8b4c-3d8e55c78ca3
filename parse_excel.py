#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def parse_excel_data():
    # 读取文件内容
    with open('2088260786994890-20250801-168426223-账务组合查询-simple.xls', 'r', encoding='utf-8') as f:
        content = f.read()

    # 使用正则表达式提取所有Row数据
    rows = re.findall(r'<Row>(.*?)</Row>', content, re.DOTALL)
    
    print(f'找到 {len(rows)} 行数据')
    
    all_data = []
    
    # 提取每行的数据
    for i, row in enumerate(rows):
        cells = re.findall(r'<Data ss:Type="String">(.*?)</Data>', row)
        if cells and len(cells) >= 22:  # 确保有22列数据
            all_data.append(cells)
            if i < 10:  # 显示前10行用于验证
                print(f'第{i+1}行: 序号={cells[0]}, 时间={cells[1]}, 交易号={cells[2]}, 订单号={cells[4]}, 类型={cells[5]}')
    
    return all_data

def generate_insert_sql(data):
    # 跳过表头行
    data_rows = data[1:] if data and data[0][0] == '序号' else data
    
    sql_statements = []
    
    for row in data_rows:
        if len(row) >= 22:
            # 处理空值和特殊字符
            def clean_value(val):
                if val.strip() == '' or val.strip() == ' ':
                    return 'NULL'
                # 转义单引号
                val = val.replace("'", "\\'")
                return f"'{val}'"
            
            def clean_decimal(val):
                if val.strip() == '' or val.strip() == ' ':
                    return '0.00'
                try:
                    return str(float(val))
                except:
                    return '0.00'
            
            # 构建INSERT语句
            sql = f"""INSERT INTO dig_asset_pay_order (
    serial_number, entry_time, alipay_trade_no, alipay_serial_no, merchant_order_no,
    account_type, income_amount, expense_amount, account_balance, service_fee,
    payment_channel, contract_product, counterpart_account, counterpart_name,
    bank_order_no, product_name, remark, business_base_order_no, business_order_no,
    business_bill_source, business_description, payment_remark
) VALUES (
    {clean_value(row[0])}, {clean_value(row[1])}, {clean_value(row[2])}, {clean_value(row[3])}, {clean_value(row[4])},
    {clean_value(row[5])}, {clean_decimal(row[6])}, {clean_decimal(row[7])}, {clean_decimal(row[8])}, {clean_decimal(row[9])},
    {clean_value(row[10])}, {clean_value(row[11])}, {clean_value(row[12])}, {clean_value(row[13])},
    {clean_value(row[14])}, {clean_value(row[15])}, {clean_value(row[16])}, {clean_value(row[17])}, {clean_value(row[18])},
    {clean_value(row[19])}, {clean_value(row[20])}, {clean_value(row[21])}
);"""
            sql_statements.append(sql)
    
    return sql_statements

if __name__ == "__main__":
    try:
        data = parse_excel_data()
        sql_statements = generate_insert_sql(data)
        
        print(f"\n生成了 {len(sql_statements)} 条INSERT语句")
        
        # 保存到文件
        with open('insert_statements.sql', 'w', encoding='utf-8') as f:
            f.write("-- INSERT语句 for dig_asset_pay_order 表\n")
            f.write("-- 基于 2088260786994890-20250801-168426223-账务组合查询-simple.xls\n\n")
            for sql in sql_statements:
                f.write(sql + "\n\n")
        
        print("INSERT语句已保存到 insert_statements.sql 文件")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
