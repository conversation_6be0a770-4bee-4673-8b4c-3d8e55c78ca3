import pandas as pd
from datetime import datetime

def export_904_asset_codes():
    """
    将dig_asset_order_2.xlsx中的ASSET_CODE按逗号拆分，导出成904个条目的新Excel文件
    """
    print("开始拆分并导出904个ASSET_CODE...")
    print("=" * 50)
    
    # 读取dig_asset_order_2.xlsx
    print("1. 读取dig_asset_order_2.xlsx...")
    df_order = pd.read_excel('dig_asset_order_2.xlsx')
    print(f"   原始文件行数: {len(df_order)}")
    print(f"   列名: {list(df_order.columns)}")
    
    # 拆分ASSET_CODE
    print("\n2. 拆分ASSET_CODE...")
    all_entries = []
    
    for index, row in df_order.iterrows():
        asset_code = row['ASSET_CODE']
        if not pd.isna(asset_code):
            asset_code_str = str(asset_code).strip()
            if asset_code_str:
                # 按逗号分割
                codes = [code.strip() for code in asset_code_str.split(',')]
                codes = [code for code in codes if code]  # 过滤空字符串
                
                # 为每个拆分出的ASSET_CODE创建一个条目
                for seq, code in enumerate(codes, 1):
                    all_entries.append({
                        'entry_id': len(all_entries) + 1,  # 全局条目ID
                        'original_row_index': index + 1,   # 原始行号
                        'ORDER_ID': row['ORDER_ID'],
                        'BUYER_ID': row['BUYER_ID'],
                        'BUY_QUANTITY': row['BUY_QUANTITY'],
                        'PAY_TIME': row['PAY_TIME'],
                        'original_asset_code': asset_code_str,  # 原始的ASSET_CODE字符串
                        'split_sequence': seq,              # 在原始字符串中的序号
                        'split_asset_code': code,           # 拆分后的单个ASSET_CODE
                        'total_splits_in_row': len(codes)   # 该行总共拆分出多少个
                    })
    
    print(f"   拆分完成，总条目数: {len(all_entries)}")
    
    # 创建DataFrame
    df_all_entries = pd.DataFrame(all_entries)
    
    # 统计信息
    print(f"\n3. 统计信息:")
    print(f"   总条目数: {len(df_all_entries)}")
    print(f"   唯一ASSET_CODE数量: {df_all_entries['split_asset_code'].nunique()}")
    print(f"   包含逗号分隔的原始行数: {df_all_entries[df_all_entries['total_splits_in_row'] > 1]['original_row_index'].nunique()}")
    
    # 显示拆分统计
    split_stats = df_all_entries['total_splits_in_row'].value_counts().sort_index()
    print(f"\n   拆分数量统计:")
    for splits, count in split_stats.items():
        print(f"     拆分为{splits}个的行数: {count}行")
    
    # 创建Excel文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"904_asset_codes_{timestamp}.xlsx"
    
    print(f"\n4. 创建Excel文件: {excel_filename}")
    
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        
        # 工作表1: 所有904个条目
        df_all_entries.to_excel(writer, sheet_name='全部904个ASSET_CODE条目', index=False)
        print(f"   工作表 '全部904个ASSET_CODE条目' 创建完成: {len(df_all_entries)} 条记录")
        
        # 工作表2: 唯一ASSET_CODE列表
        unique_codes = df_all_entries['split_asset_code'].unique()
        unique_codes_sorted = sorted(unique_codes)
        
        unique_data = []
        for i, code in enumerate(unique_codes_sorted, 1):
            # 统计这个ASSET_CODE出现的次数
            occurrences = df_all_entries[df_all_entries['split_asset_code'] == code]
            
            unique_data.append({
                'sequence': i,
                'ASSET_CODE': code,
                'occurrence_count': len(occurrences),
                'first_order_id': occurrences.iloc[0]['ORDER_ID'],
                'all_order_ids': ', '.join(map(str, occurrences['ORDER_ID'].unique())),
                'all_buyer_ids': ', '.join(map(str, occurrences['BUYER_ID'].unique()))
            })
        
        df_unique = pd.DataFrame(unique_data)
        df_unique.to_excel(writer, sheet_name='唯一ASSET_CODE列表', index=False)
        print(f"   工作表 '唯一ASSET_CODE列表' 创建完成: {len(df_unique)} 个唯一ASSET_CODE")
        
        # 工作表3: 原始行与拆分对照表
        row_summary = []
        for original_row in df_all_entries['original_row_index'].unique():
            row_entries = df_all_entries[df_all_entries['original_row_index'] == original_row]
            first_entry = row_entries.iloc[0]
            
            row_summary.append({
                'original_row_index': original_row,
                'ORDER_ID': first_entry['ORDER_ID'],
                'BUYER_ID': first_entry['BUYER_ID'],
                'BUY_QUANTITY': first_entry['BUY_QUANTITY'],
                'PAY_TIME': first_entry['PAY_TIME'],
                'original_asset_code': first_entry['original_asset_code'],
                'split_count': len(row_entries),
                'split_asset_codes': ', '.join(row_entries['split_asset_code'].tolist())
            })
        
        df_row_summary = pd.DataFrame(row_summary)
        df_row_summary = df_row_summary.sort_values('original_row_index')
        df_row_summary.to_excel(writer, sheet_name='原始行拆分对照表', index=False)
        print(f"   工作表 '原始行拆分对照表' 创建完成: {len(df_row_summary)} 行")
        
        # 工作表4: 统计汇总
        stats_data = [
            ['基本统计', ''],
            ['原始文件行数', len(df_order)],
            ['拆分后总条目数', len(df_all_entries)],
            ['唯一ASSET_CODE数量', df_all_entries['split_asset_code'].nunique()],
            ['包含逗号分隔的行数', len(df_all_entries[df_all_entries['total_splits_in_row'] > 1]['original_row_index'].unique())],
            ['', ''],
            ['拆分统计', ''],
        ]
        
        # 添加拆分统计
        for splits, count in split_stats.items():
            stats_data.append([f'拆分为{splits}个的行数', count])
        
        # 添加ASSET_CODE范围信息
        asset_numbers = []
        for code in unique_codes:
            if '#' in code:
                try:
                    num = int(code.split('#')[-1])
                    asset_numbers.append(num)
                except:
                    pass
        
        if asset_numbers:
            stats_data.extend([
                ['', ''],
                ['ASSET_CODE范围', ''],
                ['最小编号', min(asset_numbers)],
                ['最大编号', max(asset_numbers)],
                ['编号跨度', max(asset_numbers) - min(asset_numbers) + 1]
            ])
        
        df_stats = pd.DataFrame(stats_data, columns=['统计项目', '数值'])
        df_stats.to_excel(writer, sheet_name='统计汇总', index=False)
        print(f"   工作表 '统计汇总' 创建完成")
    
    print(f"\n✅ Excel文件创建成功: {excel_filename}")
    
    # 显示前10个ASSET_CODE
    print(f"\n📋 前10个ASSET_CODE:")
    for i, code in enumerate(unique_codes_sorted[:10], 1):
        count = len(df_all_entries[df_all_entries['split_asset_code'] == code])
        print(f"   {i:2d}. {code} (出现{count}次)")
    
    if len(unique_codes_sorted) > 10:
        print(f"   ... 还有 {len(unique_codes_sorted) - 10} 个")
    
    return excel_filename, len(df_all_entries), len(unique_codes_sorted)

if __name__ == "__main__":
    excel_file, total_entries, unique_count = export_904_asset_codes()
    print(f"\n🎯 导出完成:")
    print(f"文件名: {excel_file}")
    print(f"总条目数: {total_entries}")
    print(f"唯一ASSET_CODE数量: {unique_count}")
