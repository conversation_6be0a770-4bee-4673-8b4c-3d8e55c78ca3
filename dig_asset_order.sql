/*
 Navicat Premium Dump SQL

 Source Server         : 省文化数据·生产(172.20.20.2_33061)
 Source Server Type    : MySQL
 Source Server Version : 80034 (8.0.34-magnus-server)
 Source Host           : jump.tianfutv.com:33061
 Source Schema         : cwsf

 Target Server Type    : MySQL
 Target Server Version : 80034 (8.0.34-magnus-server)
 File Encoding         : 65001

 Date: 31/07/2025 17:11:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dig_asset_order
-- ----------------------------
DROP TABLE IF EXISTS `dig_asset_order`;
CREATE TABLE `dig_asset_order`  (
  `ORDER_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `ORDER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号(业务唯一)',
  `BUYER_ID` bigint NOT NULL COMMENT '买方ID',
  `SELLER_ID` bigint NULL DEFAULT NULL COMMENT '卖方ID',
  `BUY_QUANTITY` int NOT NULL COMMENT '购买数量',
  `ORDER_AMOUNT` decimal(18, 2) NOT NULL COMMENT '订单金额',
  `TRADE_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '交易类型',
  `PAY_CHANNEL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '支付渠道',
  `ASSET_ID` bigint NOT NULL COMMENT '资产ID',
  `ASSET_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '资产名称',
  `ASSET_CODE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '资产编号',
  `REFUND_ORDER_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '退款订单号',
  `PAY_TIME` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `CLOSE_TIME` datetime NULL DEFAULT NULL COMMENT '关闭时间',
  `STATUS_CD` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '状态代码',
  `CREATE_DATE` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_DATE` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `sharing_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分账规则',
  `UNION_ORDER_NO` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银联订单号',
  PRIMARY KEY (`ORDER_ID`) USING BTREE,
  UNIQUE INDEX `UK_ORDER_NO`(`ORDER_NO` ASC) USING BTREE,
  INDEX `IDX_BUYER_ID`(`BUYER_ID` ASC) USING BTREE,
  INDEX `IDX_SELLER_ID`(`SELLER_ID` ASC) USING BTREE,
  INDEX `IDX_ASSET_ID`(`ASSET_ID` ASC) USING BTREE,
  INDEX `IDX_CREATE_DATE`(`CREATE_DATE` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48587 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数字资产订单表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
